package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.item.infrastructure.price.dao.CompensateCostPriceDao;
import com.cosfo.item.infrastructure.price.dto.CompensateCostPriceDTO;
import com.cosfo.item.infrastructure.price.dto.CostPriceDTO;
import com.cosfo.item.infrastructure.price.model.CompensateCostPrice;
import com.cosfo.item.web.domain.converter.CostPriceConverter;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 手动补偿
 */
@Component
@Slf4j
public class CompensateCostPriceProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private CompensateCostPriceDao compensateCostPriceDao;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private TenantFacade tenantFacade;
    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info ("开始进行CompensateCostPriceProcessor任务");
        List<CompensateCostPrice> compensateCostPrices = null;
        if(ObjectUtil.isNull (context) || ObjectUtil.isNull (context.getInstanceParameters ())){
            return new ProcessResult(true);
        }else {
            String instanceParameters = context.getInstanceParameters ();
            CompensateCostPriceDTO compensateCostPriceDTO = JSON.parseObject (instanceParameters, CompensateCostPriceDTO.class);

            //1 从表中来
            if(compensateCostPriceDTO.getType () == 1){
                List<Long> compensateCostPriceIds = compensateCostPriceDTO.getCompensateCostPriceIds ();
                if(CollectionUtil.isEmpty (compensateCostPriceIds)){
                    return new ProcessResult(true);
                }
                //id=-99是全部，
                if(compensateCostPriceIds.contains (-99L)) {
                    compensateCostPriceIds = Collections.emptyList ();
                }
                compensateCostPrices = costPriceDomianService.listCompenstateCostPriceByIds (compensateCostPriceIds);

            }else if(compensateCostPriceDTO.getType () == 2){
            //2 手写参数
                List<CostPriceDTO> dtoList = compensateCostPriceDTO.getDtoList ();
                if(CollectionUtil.isEmpty (dtoList)){
                    return new ProcessResult(true);
                }
                compensateCostPrices = dtoList.stream ().map (CostPriceConverter::costPriceDTO2CompensateCostPrice).collect(Collectors.toList());
            }
        }
        if(CollectionUtil.isEmpty (compensateCostPrices)){
            return new ProcessResult(true);
        }
        log.info ("开始进行CompensateCostPriceProcessor任务compensateCostPrices={}",JSON.toJSONString (compensateCostPrices));
        for (CompensateCostPrice compenstateCostPrice : compensateCostPrices) {
            String skuCode = compenstateCostPrice.getSkuCode ();
            Long tenantId = compenstateCostPrice.getTenantId ();
            String province = compenstateCostPrice.getProvince ();
            String city = compenstateCostPrice.getCity ();
            String area = compenstateCostPrice.getArea ();
            SummerFarmCostPriceVO summerFarmCostPriceVO = null;
            Map<String,List<SummerFarmCostPriceVO>> addressPriceMap = new HashMap<> ();

            try {
                TenantVO tenantVO = tenantFacade.getTenantByTenantId (tenantId);
                Set<Integer> areaNos = summerfarmMallFacade.getAreaNoByAddress (province, city, area);
                if (ObjectUtil.isNotNull (tenantVO) && CollectionUtil.isNotEmpty (areaNos)) {
                    for (Integer areaNo : areaNos) {
                        summerFarmCostPriceVO = sfMallManageFacade.queryMallPriceInfo4Saas (areaNo, skuCode, tenantVO.getAdminId ());
                        addressPriceMap.computeIfAbsent(String.join("-", province, city, area), k -> new ArrayList<>()).add(summerFarmCostPriceVO);
                    }

                    costPriceDomianService.synchronize4SummerfarmBatch (skuCode, tenantId,addressPriceMap);

                    if(ObjectUtil.isNotNull (compenstateCostPrice.getId ())){
                        compensateCostPriceDao.removeById (compenstateCostPrice.getId ());
                    }
                } else {
                    log.error ("CompensateCostPriceProcessor更新跳过，tenant空，或者areano空，tenantId={},province={},city={},area={}", tenantId, province, city, area);
                }
            } catch (Exception e) {
                log.error ("CompensateCostPriceProcessor更新失败，sku={},tenantId={},area={},city={},province={},summerFarmCostPriceVO={}", skuCode, tenantId, area, city, province, JSON.toJSONString (summerFarmCostPriceVO), e);
            }
        }

        return new ProcessResult (true);
    }
}
