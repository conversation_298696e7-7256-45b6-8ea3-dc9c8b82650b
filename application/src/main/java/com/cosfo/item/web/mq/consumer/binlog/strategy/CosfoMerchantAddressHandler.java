package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * merchant_address 这张表的变更； 只为那些新增的地址，或者变化了省市区的店铺更新价格，而不是全租户更新
 */
@Slf4j
@Component
public class CosfoMerchantAddressHandler implements DbTableDmlStrategy {

    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private TenantFacade tenantFacade;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;
    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;

    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.MERCHANT_ADDRESS;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData(map -> {
                Long tenantId = Long.valueOf(map.get("tenant_id"));
                String storeIdString = map.get ("store_id");
                if(StringUtils.isNotEmpty (storeIdString)){
                    Long storeId = Long.valueOf(storeIdString);
                    String province = String.valueOf(map.get("province"));
                    String city = String.valueOf(map.get("city"));
                    String area = String.valueOf(map.get("area"));
                    Set<Integer> areaNos = summerfarmMallFacade.getAreaNoByAddress(province, city, area);
                    handleCostPrice(province, city, area, areaNos, tenantId,storeId);
                    handleItemPrice(tenantId, storeId);
                }
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey();
                Long tenantId = Long.valueOf(dataMap.get("tenant_id"));
                String province = String.valueOf(dataMap.get("province"));
                String storeIdString = dataMap.get ("store_id");
                Long storeId = null;
                if(StringUtils.isNotEmpty (storeIdString)) {
                    storeId = Long.valueOf (storeIdString);
                }
                String city = String.valueOf(dataMap.get("city"));
                String area = String.valueOf(dataMap.get("area"));

                String fullAddressNew = String.join(",", province, city, area);

                Map<String, String> oldMap = pair.getValue();
                //修改了对应地址才做变更，否则不需要同步
                if (oldMap.containsKey("province") || oldMap.containsKey("city") || oldMap.containsKey("area")) {
                    Set<Integer> areaNos = summerfarmMallFacade.getAreaNoByAddress(province, city, area);
//                    String provinceOld = oldMap.getOrDefault ("province", province);
//                    String cityOld = oldMap.getOrDefault ("city", city);
//                    String areaOld = oldMap.getOrDefault ("area", area);
//                    Integer areaNoOld = summerfarmMallFacade.getAreaNoByAddress(provinceOld, cityOld, areaOld);

//                    String fullAddressOld = String.join(",", provinceOld, cityOld, areaOld);

//                    if (!Objects.equals(areaNo, areaNoOld)) {
                        boolean result = handleCostPrice(province, city, area, areaNos, tenantId,storeId);
//                        if (!result && !tenantId.equals (xmTenantId)) {
//                            log.error("可能是鲜沐的围栏未找到, fullAddressNew:{}, fullAddressOld:{}, areaNo:{}, areaNoOld:{}", fullAddressNew, fullAddressOld, areaNo, areaNoOld);
//                        }
//                    }
                    if(!result){
                        handleItemPrice(tenantId, storeId);
                    }
                    // 鲜沐围栏未找到，不影响saas侧更新店铺价格；
//                    if (StringUtils.equalsIgnoreCase(fullAddressNew, fullAddressOld) && result) {
//                        log.warn("未更新省、市、区，不需要更新价格, dataMap:{}, oldData:{}", JSON.toJSONString(dataMap), JSON.toJSONString(oldMap));
//                    } else {
//                        log.info("店铺:{} 更新了省、市、区，更新店铺的价格和上下架策略, dataMap:{}, oldData:{}", storeId, JSON.toJSONString(dataMap), JSON.toJSONString(oldMap));
//                        handleItemPrice(tenantId, storeId);
//                    }
                }
            }
        }
    }

    /**
     * 只为那些新增的地址，或者变化了省市区的店铺更新价格，而不是全租户更新
     * 省市区变更，不可以只更新报价商品的价格,而不用更新虚拟商品、自营货品的价格，因为店铺新增需要为所有的商品初始化价格。@2023-06-29
     *
     * @param tenantId
     * @param storeId
     */
    private void handleItemPrice(Long tenantId, Long storeId) {
        if(xmTenantId.equals (tenantId)) {
            return;
        }
        if(ObjectUtil.isNull (storeId)) {
            return;
        }
        List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemByTenantIdAndTypes(tenantId, Arrays.asList(MarketItemEnum.GoodsType.SELF_SUPPORT.getCode(), MarketItemEnum.GoodsType.VIRTUAL.getCode()));
        if (CollectionUtils.isEmpty(marketItemVOS)) {
            log.warn("没有找到marketItemVOS：{}", tenantId);
            return;
        }
        log.info("店铺:{} 地址省市区变化，更新店铺所有的item价格", storeId);
        for (MarketItemVO marketItemVO : marketItemVOS) {
            ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
            dto.setStoreId (storeId);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }

    /**
     * @param province
     * @param city
     * @param area
     * @param areaNos
     * @param tenantId
     * @return 是否有更新；true：有，false：没有更新，可以忽略
     */
    private boolean handleCostPrice(String province, String city, String area, Set<Integer> areaNos, Long tenantId,Long storeId) {
        if(xmTenantId.equals (tenantId)) {
            return false;
        }
        if (ObjectUtil.isNull(province) || ObjectUtil.isNull(city) || ObjectUtil.isNull(area) || CollectionUtil.isEmpty (areaNos)) {
            return false;
        }
        TenantVO tenantVO = tenantFacade.getTenantByTenantId(tenantId);
        if (ObjectUtil.isEmpty(tenantVO)) {
            return false;
        }

        List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemByTenantIdAndType(tenantId, MarketItemEnum.GoodsType.QUOTATION.getCode());
        if (ObjectUtil.isEmpty(marketItemVOS)) {
            return false;
        }
        Set<Long> skuIds = marketItemVOS.stream().filter(e -> ObjectUtil.isNotNull(e.getSkuId())).map(MarketItemVO::getSkuId).collect(Collectors.toSet());
        List<ProductAgentSkuMappingVO> productAgentSkuMappingVOS = productFacade.listProductMappingBySkuIdsAndAgentTenantId(skuIds, xmTenantId);
        for (ProductAgentSkuMappingVO productAgentSkuMapping : productAgentSkuMappingVOS) {
            String sku = productAgentSkuMapping.getAgentSkuCode();
            SummerFarmCostPriceVO avg = null;
            try {
                List<SummerFarmCostPriceVO> list = new ArrayList<>();
                for(Integer areaNo : areaNos) {
                    SummerFarmCostPriceVO summerFarmCostPriceVO = sfMallManageFacade.queryMallPriceInfo4Saas(areaNo, sku, tenantVO.getAdminId());
                    list.add (summerFarmCostPriceVO);
                }
                avg = costPriceDomianService.getAvg (list, sku);
                boolean hasCostPriceFlag = costPriceDomianService.synchronize4Summerfarm (sku, tenantId, area, city, province, avg);
                if(!hasCostPriceFlag){
                    marketItemVOS.stream().filter (e->Objects.equals (productAgentSkuMapping.getSkuId (),e.getSkuId ()))
                            .forEach (marketItemVO-> {
                                ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
                                dto.setStoreId (storeId);
                                marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
                            });
                }
            } catch (Exception e) {
                costPriceDomianService.saveCompenstateCostPrice(sku, tenantVO.getId(), area, city, province, e.toString());
                log.error("MerchantAddress变更 cost_price更新失败，sku={},tenantId={},area={},city={},province={},summerFarmCostPriceVO={}", sku, tenantId, area, city, province,
                    JSON.toJSONString(avg), e);
            }
        }
        return true;
    }
}
