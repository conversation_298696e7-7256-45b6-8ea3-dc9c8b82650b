package com.cosfo.item.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cosfo.item.common.dto.LadderPriceDTO;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.infrastructure.item.dao.MarketCombineItemMappingDao;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketItemParam;
import com.cosfo.item.infrastructure.item.model.Market;
import com.cosfo.item.infrastructure.item.model.MarketCombineItemMapping;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyDao;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.web.domain.converter.PriceConvert;
import com.cosfo.item.web.domain.dto.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.MerchantStoreFacade;
import com.cosfo.item.web.skuPreferential.domain.SkuPreferentialDomainService;
import net.xianmu.common.exception.BizException;
import net.xianmu.i18n.exception.I18nBizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 价格相关
 * MarketItemPriceDomianService、CostPriceDomianService 是他的子域，后续有营销活动时 在此处做价格的相关查询、包装 和处理
 */
@Component
public class PriceDomianService {

    @Autowired
    private MarketItemPriceDomianService itemPriceDomianService;

    @Autowired
    private MarketCombineItemMappingDao combineItemMappingDao;

    @Autowired
    private MarketItemDao marketItemDao;

    @Autowired
    private ItemDomainService itemDomainService;

    @Autowired
    private CostPriceDomianService costPriceDomianService;

    @Autowired
    private UnfairPriceStrategyDomainService unfairPriceStrategyDomainService;

    @Autowired
    private MarketItemPriceStrategyDomainService priceStrategyDomainService;

    @Autowired
    private MarketItemPriceStrategyDao strategyDao;

    @Autowired
    private MerchantStoreFacade merchantStoreFacade;
    /**
     * 批量查询组合品 每个子item的价格详情
     * @return
     */
    public List<ItemPriceDetailVO> listItemPriceDetailByItemIds4CombineItem(Long tenantId, Long storeId, List<Long> itemIds) {
        Map<Long, MarketItemPriceStrategyVO> priceStrategyMap = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds (tenantId, itemIds).stream ().collect (Collectors.toMap (MarketItemPriceStrategyVO::getItemId, Function.identity()));

        List<MarketCombineItemMapping> subItemMappings = combineItemMappingDao.listByParam(MarketCombineQueryParam.builder()
                .itemIds(itemIds)
                .build());
        List<Long> combineItemIds = subItemMappings.stream ().map (MarketCombineItemMapping::getCombineItemId).collect (Collectors.toList ());
        itemIds.addAll (combineItemIds);

        Map<Long, List<MarketCombineItemMapping>> subItemMap = subItemMappings.stream().collect(Collectors.groupingBy(MarketCombineItemMapping::getItemId));

        Map<Long, PriceDetailVO> priceDetailVOMap = listItemPriceDetailByItemIds (tenantId, storeId, MarketItemPriceStrategyEnum.TargetTypeEnum.STORE.getCode (), itemIds);

        List<ItemPriceDetailVO> result = new ArrayList<> ();
        subItemMap.forEach ((itemId,subItems)->{
            ItemPriceDetailVO priceDetailVO = PriceConvert.priceDetailVO2ItemPriceDetailVO(priceDetailVOMap.get (itemId),itemId);
            List<ItemPriceDetailVO> subItemPriceDetails = subItems.stream ().map (sub -> {
                ItemPriceDetailVO subPriceDetail = PriceConvert.priceDetailVO2ItemPriceDetailVO (priceDetailVOMap.get (sub.getCombineItemId ()),sub.getCombineItemId ());
                subPriceDetail.setPrice(getPrice(priceStrategyMap.get (itemId),priceDetailVO.getCostPrice (),subPriceDetail.getMarketItemPrice (),sub.getQuantity ()));
                return subPriceDetail;
            }).collect (Collectors.toList ());
            priceDetailVO.setSubItemPriceDetails(subItemPriceDetails);
            result.add (priceDetailVO);
        });
        return result;
    }
    /**
     * 批量查询组合品 每个子item的价格详情
     * @return
     */
    public List<ItemPriceDetailVO> listItemPriceDetailByItemIds4CombineItem(Long tenantId, Long storeId, Map<Long, Integer> itemBuyAmount,boolean throwExceptionFlag) {
        List<Long> itemIds = new ArrayList<> (itemBuyAmount.keySet ());
        Map<Long, MarketItemPriceStrategyVO> priceStrategyMap = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds (tenantId, itemIds).stream ().collect (Collectors.toMap (MarketItemPriceStrategyVO::getItemId, Function.identity()));

        List<MarketCombineItemMapping> subItemMappings = combineItemMappingDao.listByParam(MarketCombineQueryParam.builder()
                .itemIds(itemIds)
                .build());
        List<Long> combineItemIds = subItemMappings.stream ().map (MarketCombineItemMapping::getCombineItemId).collect (Collectors.toList ());
        itemIds.addAll (combineItemIds);

        Map<Long, List<MarketCombineItemMapping>> subItemMap = subItemMappings.stream().collect(Collectors.groupingBy(MarketCombineItemMapping::getItemId));
        Map<Long, Integer> itemIdMap = subItemMap.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream()
                        .map(sub -> new AbstractMap.SimpleEntry<>(sub.getCombineItemId(), itemBuyAmount.get(entry.getKey()) * sub.getQuantity())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, replacement) -> existing));
        itemIdMap.putAll (itemBuyAmount);

        Map<Long, PriceDetailVO> priceDetailVOMap = listItemPriceDetailByItemIdsWithQuantity (tenantId, storeId, MarketItemPriceStrategyEnum.TargetTypeEnum.STORE.getCode (), itemIdMap, throwExceptionFlag);

        List<ItemPriceDetailVO> result = new ArrayList<> ();
        subItemMap.forEach ((itemId,subItems)->{
            ItemPriceDetailVO priceDetailVO = PriceConvert.priceDetailVO2ItemPriceDetailVO(priceDetailVOMap.get (itemId),itemId);
            BigDecimal price = BigDecimal.ZERO;
            List<ItemPriceDetailVO> subItemPriceDetails = new ArrayList<> ();
            for(MarketCombineItemMapping sub : subItems) {
                ItemPriceDetailVO subPriceDetail = PriceConvert.priceDetailVO2ItemPriceDetailVO (priceDetailVOMap.get (sub.getCombineItemId ()),sub.getCombineItemId ());
                subPriceDetail.setPrice(getPrice(priceStrategyMap.get (itemId),priceDetailVO.getCostPrice (),subPriceDetail.getMarketItemPrice (),sub.getQuantity ()));
                if(subPriceDetail.getPrice () !=null) {
                    price = price.add (subPriceDetail.getPrice ().multiply (new BigDecimal (sub.getQuantity ())));
                }
                subItemPriceDetails.add (subPriceDetail);
            }
            priceDetailVO.setSubItemPriceDetails(subItemPriceDetails);
            priceDetailVO.setPrice (price);
            result.add (priceDetailVO);
        });
        return result;
    }

    private BigDecimal getPrice(MarketItemPriceStrategyVO priceStrategyVO,BigDecimal totalPrice,BigDecimal marketItemPrice,Integer quantity) {
        if (Objects.isNull(priceStrategyVO) || Objects.isNull(totalPrice) || Objects.isNull(marketItemPrice) || Objects.isNull(quantity)){
            return null;
        }
        MarketItemPriceStrategyEnum.StrategyTypeEnum strategyTypeEnum = MarketItemPriceStrategyEnum.StrategyTypeEnum.of (priceStrategyVO.getStrategyType ());
        switch (strategyTypeEnum){
            case SALE_PRICE_REDUCE_PERCENTAGE:
                return NumberUtil.div(NumberUtil.mul(marketItemPrice, NumberUtil.sub(BigDecimal.valueOf(100), priceStrategyVO.getStrategyValue())),BigDecimal.valueOf(100), 2);
            case SALE_PRICE_REDUCE_FIXED:
                //原价 - （（（（原价*数量） / 原总价） * 扣减的固定值）/数量）
                BigDecimal sub = NumberUtil.sub (marketItemPrice, NumberUtil.div (NumberUtil.mul (NumberUtil.div (NumberUtil.mul (marketItemPrice, quantity), totalPrice, 8), priceStrategyVO.getStrategyValue ()), quantity, 2));
                return sub.compareTo (BigDecimal.ZERO) == -1?BigDecimal.ZERO:sub;
            case SALE_PRICE_TOTAL:
                return marketItemPrice;
            default:
                return null;
        }
    }


    /**
     * 根据 报价价格目标类型和价格目标id 批量查询商品唯一价格详情
     * return map<itemId,price>
     * 如果有门店价格 返回门店价格
     * 否则是否有区域价格 返回区域价格
     * 否则是否有租户价格 返回租户价格
     *
     * @param tenantId
     * @param targetId   门店/区域id
     * @param targetType 门店/区域
     * @param itemIds
     * @return
     */
    public Map<Long, PriceDetailVO> listItemPriceDetailByItemIds(Long tenantId, Long targetId, Integer targetType, List<Long> itemIds) {
        Map<Long,PriceDetailVO> priceVOMap = itemPriceDomianService.listAllPriceByItemIds(tenantId, itemIds,targetId,targetType);
        Map<Long, PriceDetailVO> result = new HashMap<> ();
        if (Objects.equals(targetType, PriceTargetTypeEnum.STORE.getCode())) {
            Map<Long, BigDecimal> costPriceMap = getCostPriceMap (tenantId, targetId, itemIds);
            priceVOMap.forEach((marketItemId, priceVO) -> {
                if (Objects.nonNull(priceVO)) {
                    priceVO.setCostPrice (costPriceMap.getOrDefault (marketItemId, priceVO.getCostPrice()));
                    result.put(marketItemId, priceVO);
                }
            });
        } else if (Objects.equals(targetType, PriceTargetTypeEnum.AREA_NO.getCode())) {
            priceVOMap.forEach((marketItemId, priceVO) -> {
                if (Objects.nonNull(priceVO)) {
                    result.put(marketItemId, priceVO);
                }
            });
        }
        return result;
    }

    /**
     * 根据 报价价格目标类型和价格目标id 批量查询商品唯一价格详情
     * return map<itemId,price>
     * 如果有门店价格 返回门店价格
     * 否则是否有区域价格 返回区域价格
     * 否则是否有租户价格 返回租户价格
     *
     * @param tenantId
     * @param targetId   门店/区域id
     * @param targetType 门店/区域
     * @return
     */
    public Map<Long, PriceDetailVO> listItemPriceDetailByItemIdsWithQuantity(Long tenantId, Long targetId, Integer targetType, Map<Long,Integer> itemIdMap,boolean throwExceptionFlag) {
        List<Long> itemIds = new ArrayList<> (itemIdMap.keySet ());
//        查询倒挂策略
        Map<Long, UnfairPriceStrategyVO> unfairPriceStrategyMap = unfairPriceStrategyDomainService.getStrategyValueByItemIdsAndTargetType (tenantId, itemIds, null);
        Map<Long,PriceDetailVO> priceVOMap = itemPriceDomianService.listAllPriceByItemIds(tenantId, itemIds,targetId,targetType);
        Map<Long, PriceDetailVO> result = new HashMap<> ();
        if (Objects.equals(targetType, PriceTargetTypeEnum.STORE.getCode())) {
            Map<Long, BigDecimal> costPriceMap = getCostPriceMap (tenantId, targetId, itemIds);
            priceVOMap.forEach((marketItemId, priceVO) -> {
                if (Objects.nonNull(priceVO)) {
                    priceVO.setCostPrice (costPriceMap.getOrDefault (marketItemId, priceVO.getCostPrice()));
                    if(CollectionUtil.isNotEmpty (priceVO.getLadderPriceDTOS ()) && priceVO.getLadderPriceDTOS ().size ()>1) {
                        dealPrice (priceVO, priceVO.getLadderPriceDTOS (), unfairPriceStrategyMap.get (marketItemId), itemIdMap.get (marketItemId), marketItemId, throwExceptionFlag);
                    }
                    result.put(marketItemId, priceVO);
                }
            });
        } else if (Objects.equals(targetType, PriceTargetTypeEnum.AREA_NO.getCode())) {
            priceVOMap.forEach((marketItemId, priceVO) -> {
                if (Objects.nonNull(priceVO)) {
                    result.put(marketItemId, priceVO);
                }
            });
        }
        return result;
    }

    private void dealPrice(PriceDetailVO priceVO, List<LadderPriceDTO> ladderPriceDTOS, UnfairPriceStrategyVO unfairPriceStrategyVO,Integer quantity,Long marketItemId,boolean throwExceptionFlag) {
        BigDecimal price = priceVO.getPrice ();
        BigDecimal costPrice = priceVO.getCostPrice ();
        int unit = 1;

        if(CollectionUtil.isNotEmpty (ladderPriceDTOS)){
            // 遍历列表，查找第一个 unit 大于或等于给定值的 LadderPriceDTO
            for (LadderPriceDTO ladderPriceDTO : ladderPriceDTOS) {
                if (quantity >= ladderPriceDTO.getUnit()) {
                    price = ladderPriceDTO.getPrice ();
                    unit = ladderPriceDTO.getUnit ();
                }
            }
        }
        //倒挂
        if (costPrice !=null && price!=null && costPrice.compareTo (price) > 0) {
            MarketItemUnfairPriceStrategyEnum.StrategyValueEnum strategyValue = unfairPriceStrategyVO.getStrategyValue ();
            MarketItemCommonQueryDTO queryDTO = new MarketItemCommonQueryDTO();
            queryDTO.setItemId (marketItemId);
            queryDTO.setPageNum (1);
            queryDTO.setPageSize (1);
            Page<MarketItemVO> pageInfo;
            switch (strategyValue) {
                case USE_COST_PRICE:
                    if(quantity == 1) {
                        price = costPrice;
                        break;
                    }else{
                        if(throwExceptionFlag){
                            pageInfo = itemDomainService.querySimpleMarketItemList(queryDTO);
                            int max = findMaxUnit(ladderPriceDTOS,costPrice);
                            throw new I18nBizException ("{0}商品最多只可买{1}个",pageInfo.getRecords ().get (0).getTitle () ,max);
                        }
                    }
                case AUTO_SOLD_OUT:
                    if(throwExceptionFlag) {
                        pageInfo = itemDomainService.querySimpleMarketItemList (queryDTO);
                        throw new I18nBizException ("{0}商品价格倒挂不允许购买",pageInfo.getRecords ().get (0).getTitle ());
                    }
                default:
                    break;
                }
        }
        priceVO.setPrice (price);
    }
    private int findMaxUnit(List<LadderPriceDTO> ladderPriceDTOS, BigDecimal givenPrice) {
        int maxUnit = Integer.MIN_VALUE; // 初始化为最小值
        boolean found = false; // 用于检查是否找到了符合条件的价格

        for (LadderPriceDTO ladderPriceDTO : ladderPriceDTOS) {
            // 检查价格是否大于或等于给定值
            if (ladderPriceDTO.getPrice().compareTo(givenPrice) >= 0) {
                found = true; // 找到符合条件的价格
                // 更新最大的 unit 值
                if (ladderPriceDTO.getUnit() > maxUnit) {
                    maxUnit = ladderPriceDTO.getUnit();
                }
            }
        }

        // 如果没有找到符合条件的项，可以根据需求返回特定值或抛出异常等
        if (!found) {
            return 1;
        }

        return maxUnit;
    }
    private Map<Long,BigDecimal> getCostPriceMap(Long tenantId,Long storeId,List<Long> itemIds){
        //  查询价格策略
        List<MarketItemPriceStrategyVO> marketItemPriceStrategyVOS = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds (tenantId, itemIds);
        List<MarketItemPriceStrategyVO> assignPriceStrategyVOs = marketItemPriceStrategyVOS.stream ().filter (e->e.getStrategyType ().equals (MarketItemPriceStrategyEnum.StrategyTypeEnum.ASSIGN.getCode ())).collect (Collectors.toList ());

        //查询固定架的成本价格
        Map<Long,BigDecimal> costPriceMap = new HashMap<> ();
        if(CollectionUtil.isNotEmpty (assignPriceStrategyVOs)){
//            过滤自营商品
            List<MarketItem> marketItems = marketItemDao.listByIds (assignPriceStrategyVOs.stream ().map (MarketItemPriceStrategyVO::getItemId).collect(Collectors.toSet ())).stream().filter (e->MarketItemEnum.GoodsType.SELF_SUPPORT.getCode().equals(e.getGoodsType ())).collect(Collectors.toList());
            Set<Long> skuIds = marketItems.stream ().filter (marketItem -> ObjectUtil.isNotNull (marketItem.getSkuId ())).map (MarketItem::getSkuId).collect (Collectors.toSet ());
            if(CollectionUtil.isNotEmpty (skuIds)) {
                MerchantStoreAddressVO merchantStoreAddressVO = merchantStoreFacade.queryStoreAddress (tenantId,storeId);
                if(ObjectUtil.isEmpty (merchantStoreAddressVO)){
                    throw new BizException ("门店不存在");
                }
                MerchantStoreAddressDTO addressDTO = new MerchantStoreAddressDTO ();
                addressDTO.setStoreId(merchantStoreAddressVO.getStoreId ());
                addressDTO.setProvince(merchantStoreAddressVO.getProvince ());
                addressDTO.setCity(merchantStoreAddressVO.getCity ());
                addressDTO.setArea(merchantStoreAddressVO.getArea ());
                addressDTO.setCityId(merchantStoreAddressVO.getCityId ());
                List<CostPriceVO> costCityPriceVOS = costPriceDomianService.listCostPriceBySkuIds (skuIds, addressDTO, tenantId);
                Map<Long,BigDecimal> costCityPriceVOMap = costCityPriceVOS.stream().collect(Collectors.toMap (CostPriceVO::getFtSkuId,CostPriceVO::getPrice));
                marketItems.forEach (item-> costPriceMap.put (item.getId (), costCityPriceVOMap.getOrDefault (item.getSkuId (), BigDecimal.ZERO)));
            }
        }
        return costPriceMap;
    }
    /**
     * 批量修改价格策略
     */
    public void batchUpdatePriceStrategy(PriceStrategyFloatingRangeDTO dto) {
        priceStrategyDomainService.batchUpdatePriceStrategy (dto);
    }
    /**
     * 批量查询 原价格策略区间
     */
    public PriceStrategyRangeVO batchQueryPriceStrategy(Long tenantId,List<Long> marketItemIds) {
        PriceStrategyRangeVO priceStrategyRangeVO = new PriceStrategyRangeVO ();
        List<MarketItemPriceStrategy> strategys = strategyDao.listByItemIds(tenantId,marketItemIds);
        if(CollectionUtil.isNotEmpty (strategys)){
            Map<Integer, List<MarketItemPriceStrategy>> strategyMap = strategys.stream().collect(Collectors.groupingBy(MarketItemPriceStrategy::getStrategyType));
            List<MarketItemPriceStrategy> costPriceAddPercentageList = strategyMap.get (MarketItemPriceStrategyEnum.StrategyTypeEnum.COST_PRICE_ADD_PERCENTAGE.getCode ());
            List<MarketItemPriceStrategy> costPriceAddFixedList = strategyMap.get (MarketItemPriceStrategyEnum.StrategyTypeEnum.COST_PRICE_ADD_FIXED.getCode ());
            List<MarketItemPriceStrategy> assignList = strategyMap.get (MarketItemPriceStrategyEnum.StrategyTypeEnum.ASSIGN.getCode ());
            if(CollectionUtil.isNotEmpty (costPriceAddPercentageList)){
                priceStrategyRangeVO.setCostPriceAddPercentageMin(costPriceAddPercentageList.stream().map (MarketItemPriceStrategy::getStrategyValue).min(Comparator.comparing(x -> x)).orElse(null));
                priceStrategyRangeVO.setCostPriceAddPercentageMax(costPriceAddPercentageList.stream().map (MarketItemPriceStrategy::getStrategyValue).max(Comparator.comparing(x -> x)).orElse(null));
            }
            if(CollectionUtil.isNotEmpty (costPriceAddFixedList)){
                priceStrategyRangeVO.setCostPriceAddFixedMin(costPriceAddFixedList.stream().map (MarketItemPriceStrategy::getStrategyValue).min(Comparator.comparing(x -> x)).orElse(null));
                priceStrategyRangeVO.setCostPriceAddFixedMax(costPriceAddFixedList.stream().map (MarketItemPriceStrategy::getStrategyValue).max(Comparator.comparing(x -> x)).orElse(null));
            }
            if(CollectionUtil.isNotEmpty (assignList)){
                List<BigDecimal> collect = assignList.stream ().map (MarketItemPriceStrategy::getStrategyValue).collect (Collectors.toList ());
                List<BigDecimal> mergedList = assignList.stream()
                        .filter(e -> StringUtils.isNotBlank(e.getPriceStrategyValue())) // 过滤掉空值
                        .flatMap(p -> JSON.parseArray(p.getPriceStrategyValue(), LadderPriceDTO.class).stream()
                                .map(LadderPriceDTO::getPrice)) // 提取价格
                        .collect(Collectors.toList()); // 收集到 mergedList
                if(CollectionUtil.isNotEmpty (mergedList)){
                    collect.addAll (mergedList);
                }
                priceStrategyRangeVO.setAssignMin(collect.stream().min(Comparator.comparing(x -> x)).orElse(null));
                priceStrategyRangeVO.setAssignMax(collect.stream().max(Comparator.comparing(x -> x)).orElse(null));
            }
        }
        return priceStrategyRangeVO;
    }
    /**
     * 查询最高供应价
     */
    public BigDecimal queryMaxCostPrice(MaxCostPriceQueryDTO dto) {
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(dto.getTenantId (),Collections.singletonList (dto.getSkuId ()));
        if(CollectionUtil.isEmpty (marketItems)){
            return null;
        }
        Integer goodsType = marketItems.stream ().findFirst ().get ().getGoodsType ();
        List<CostPriceVO> costCityPriceVOS = costPriceDomianService.listCostPriceBySkuId (dto.getSkuId (), new HashSet<> (dto.getAddressDTOS ()), dto.getTenantId (),goodsType);
        return costCityPriceVOS.stream ().map (CostPriceVO::getPrice).max (Comparator.comparing (x -> x)).orElse (null);
    }
}
