package com.cosfo.item.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.web.domain.vo.ProvinceCityAreaVO;
import com.cosfo.item.web.domain.vo.SummerFarmCostPriceVO;
import com.cosfo.item.web.facade.converter.SummerfarmMallConverter;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.req.SkuBaseInfoReq;
import net.summerfarm.mall.client.resp.MallPrice4SaasResp;
import net.summerfarm.mall.client.saas.model.AddressInfoVO;
import net.summerfarm.mall.client.saas.provider.AddressInfoProvider;
import net.summerfarm.mall.client.saas.req.AddressInfoReq;
import net.summerfarm.mall.client.saas.req.AreaNoByAddressReq;
import net.summerfarm.mall.client.saas.resp.AddressInfoResp;
import net.summerfarm.mall.client.saas.resp.AreaNoByAddressResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import net.summerfarm.mall.client.provider.SkuProvider;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SummerfarmMallFacade {

    @DubboReference
    private SkuProvider SkuProvider;
    @DubboReference
    private AddressInfoProvider addressInfoProvider;

    /**
     * 根据运营区域查询鲜沐价格
     * @param areaNo
     * @param sku
     * @param adminId
     * @return
     */
    @Deprecated
    public SummerFarmCostPriceVO queryMallPriceInfo4Saas(Integer areaNo, String sku, Long adminId){
        SkuBaseInfoReq req = new SkuBaseInfoReq();
        req.setAreaNo(areaNo);
        req.setSku(sku);
        req.setAdminId(adminId);
        DubboResponse<MallPrice4SaasResp> response = SkuProvider.queryMallPriceInfo4Saas(req);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        MallPrice4SaasResp data = response.getData();
        if(!ObjectUtil.isEmpty(data)){
            return SummerfarmMallConverter.mallPrice4SaasResp2VO(data);
        }
        return null;
    }
    /**
     * 查询运营区域对应的省市区
     * @param areaNo
     * @return
     */
    public List<ProvinceCityAreaVO> getAddressInfoByAreaNoInternal(Integer areaNo){
        AddressInfoReq req = new AddressInfoReq();
        req.setAreaNo(areaNo);
        DubboResponse<AddressInfoResp> response = addressInfoProvider.getAddressInfo(req);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        AddressInfoResp data = response.getData ();
        if(!ObjectUtil.isEmpty(data) && CollectionUtil.isNotEmpty (data.getAddressInfoVOS ())){
            return data.getAddressInfoVOS ().stream ().map(SummerfarmMallConverter::addressInfoResp2VO).collect(Collectors.toList());
        }
        return Collections.emptyList ();
    }

    public List<ProvinceCityAreaVO> getAddressInfoByAreaNo(Integer areaNo) {
        try {
            log.info("从缓存中getAddressInfoByAreaNo，areano:{}", areaNo);
            return AREA_CACHE.get(areaNo);
        } catch (Exception e) {
            log.warn("从缓存中getAddressInfoByAreaNo失败:{}", areaNo, e);
            return getAddressInfoByAreaNoInternal(areaNo);
        }
    }

    private final LoadingCache<Integer, List<ProvinceCityAreaVO>> AREA_CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .recordStats()
            .expireAfterWrite(Duration.ofSeconds(5 * 60))//5分钟
            .build(new CacheLoader<Integer, List<ProvinceCityAreaVO>> () {
                @Override
                public List<ProvinceCityAreaVO> load(Integer areaNo) {
                    return getAddressInfoByAreaNoInternal(areaNo);
                }
            });

    public void invalidateAreaCache(Integer areaNo){
        AREA_CACHE.invalidate(areaNo);
    }

    /**
     * 查询省市区对应的运营区域
     * @param addressList
     * @return
     */
    public Map<Integer,List<ProvinceCityAreaVO>> listAreaNoByAddressString(List<String> addressList){
        AreaNoByAddressReq req = new AreaNoByAddressReq ();
        if(CollectionUtil.isEmpty (addressList)){
            return Collections.emptyMap ();
        }
        List<AddressInfoVO> reqVos = addressList.stream ().filter (ObjectUtil::isNotNull).map (address -> {
            List<String> pca = Splitter.on ("-").splitToStream (address).map (String::valueOf).collect (Collectors.toList ());
            AddressInfoVO addressInfoVO = new AddressInfoVO ();
            addressInfoVO.setProvinceName (pca.get (0));
            if(pca.size () > 0) {
                addressInfoVO.setCityName (pca.get (1));
            }
            if(pca.size () > 1) {
                addressInfoVO.setAreaName (pca.get (2));
            }
            return addressInfoVO;
        }).collect (Collectors.toList ());
        req.setAddressInfoVOS (reqVos);

        DubboResponse<AreaNoByAddressResp> response = addressInfoProvider.getAreaNoByAddress (req);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        AreaNoByAddressResp data = response.getData();
        Map<Integer,List<ProvinceCityAreaVO>> result = new HashMap<> ();
        if(ObjectUtil.isNotNull (data) && CollectionUtil.isNotEmpty (data.getMaps ())){
            Map<Integer, List<AddressInfoVO>> maps = data.getMaps ();
            maps.forEach ((areaNo,addressInfoVOList)-> result.put(areaNo,addressInfoVOList.stream ().map(SummerfarmMallConverter::addressInfoResp2VO).collect(Collectors.toList())));
        }
        return result;
    }

    public Set<Integer> getAreaNoByAddress(String province, String city, String area) {
        AreaNoByAddressReq req = new AreaNoByAddressReq ();
        AddressInfoVO addressInfoReq = new AddressInfoVO ();
        addressInfoReq.setProvinceName(province);
        addressInfoReq.setCityName(city);
        addressInfoReq.setAreaName(area);
        req.setAddressInfoVOS (Collections.singletonList (addressInfoReq));

        DubboResponse<AreaNoByAddressResp> response = addressInfoProvider.getAreaNoByAddress (req);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        AreaNoByAddressResp data = response.getData();
        if(ObjectUtil.isNotNull (data) && CollectionUtil.isNotEmpty (data.getMaps ())){
            Map<Integer, List<AddressInfoVO>> maps = data.getMaps ();
            return maps.keySet ();
        }
        return null;
    }
}
