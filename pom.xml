<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>
    <groupId>com.cosfo</groupId>
    <artifactId>item</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>




    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <app.version>1.0-SNAPSHOT</app.version>
        <app.client.version>1.0.53-RELEASE</app.client.version>
        <xianmu-common.version>1.1.15-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.14-RELEASE</xianmu-dubbo.version>
        <xianmu.i18n.sdk.version>1.0.2-RELEASE</xianmu.i18n.sdk.version>

        <xianmu-task-support.version>1.0.5</xianmu-task-support.version>
        <xianmu-rocket-mq.version>1.2.1</xianmu-rocket-mq.version>

        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.18.2</lombok.version>
        <starter.version>2.1.1</starter.version>
        <mysql-connector.version>8.0.22</mysql-connector.version>
        <druid.version>1.1.21</druid.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <dubbo-registry-nacos.version>2.7.15</dubbo-registry-nacos.version>
        <rocketmq.starter.version>2.1.1</rocketmq.starter.version>
        <redisson.version>3.11.3</redisson.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <velocity.version>2.2</velocity.version>
        <hutool.version>5.7.22</hutool.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <cosfo-manage-client.version>1.3.1-RELEASE</cosfo-manage-client.version>
        <usercenter-client.version>1.0.7</usercenter-client.version>
        <mall-client.version>1.0.8-RELEASE</mall-client.version>
        <gauva.version>30.1-jre</gauva.version>
        <org.mapstruct.version>1.3.1.Final</org.mapstruct.version>
        <mybatis.plus.join.version>1.2.4</mybatis.plus.join.version>
        <mybatis-plus-dynamic-datasource.version>3.5.0</mybatis-plus-dynamic-datasource.version>
        <guava.version>30.1-jre</guava.version>
        <usercenter-client.version>1.0.6</usercenter-client.version>
        <goodscenter-client.version>1.0.3-RELEASE</goodscenter-client.version>
        <sentinel.version>1.0.1-RELEASE</sentinel.version>
        <sf-manage-client.version>1.0.6-RELEASE</sf-manage-client.version>
    </properties>

    <dependencyManagement>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependencies>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>item-common</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>item-application</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>item-infrastructure</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>item-center-client</artifactId>
                <version>${app.client.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.i18n</groupId>
                <artifactId>xianmu-i18n-sdk</artifactId>
                <version>${xianmu.i18n.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${xianmu-task-support.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-rocket-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>cosfo-manage-client</artifactId>
                <version>${cosfo-manage-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>mall-client</artifactId>
                <version>${mall-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>sf-mall-manage-client</artifactId>
                <version>${sf-manage-client.version}</version>
            </dependency>
            <!--    用户中心-->
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${usercenter-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>goods-center-client</artifactId>
                <version>${goodscenter-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-sentinel-support</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->



            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <!-- 数据库组件——mysql连接组件 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- alibaba开源数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 注册中心 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo-registry-nacos.version}</version>
            </dependency>
            <!-- redis依赖 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${mybatis-plus-dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- rocket mq -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.3.1.RELEASE</version>
            </dependency>
            <!-- alibaba json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <!-- mybatis 联表查询 -->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join</artifactId>
                <version>${mybatis.plus.join.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
                <scope>compile</scope>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>application</module>
        <module>infrastructure</module>
        <module>common</module>
    </modules>
</project>
